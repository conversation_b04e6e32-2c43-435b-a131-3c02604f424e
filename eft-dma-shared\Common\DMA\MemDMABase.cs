﻿global using static eft_dma_shared.Common.DMA.MemoryInterface;
using VmmFrost;
using System.Diagnostics;
using System.Text;
using System.Runtime.InteropServices;
using System.Runtime.CompilerServices;
using eft_dma_shared.Common.Misc;
using eft_dma_shared.Common.DMA.ScatterAPI;
using eft_dma_shared.Common.Misc.Commercial;
using eft_dma_shared.Common.Unity.LowLevel.Hooks;

namespace eft_dma_shared.Common.DMA
{
    internal static class MemoryInterface
    {
        private static MemDMABase _memory;
        /// <summary>
        /// Limited Singleton Instance for use in this satelite assembly.
        /// </summary>
        public static MemDMABase Memory
        {
            get => _memory;
            internal set => _memory ??= value;
        }
    }
    /// <summary>
    /// DMA Memory Module.
    /// </summary>
    public abstract class MemDMABase
    {
        #region Init

        private const string _memoryMapFile = "mmap.txt";
        public const uint MAX_READ_SIZE = (uint)0x1000 * 1500;
        protected static readonly ManualResetEvent _syncProcessRunning = new(false);
        protected static readonly ManualResetEvent _syncInRaid = new(false);
        protected readonly Vmm _hVMM;
        protected uint _pid;
        protected bool _restartRadar;

        /// <summary>
        /// Current Process ID (PID).
        /// </summary>
        public uint PID => _pid;
        public ulong MonoBase { get; protected set; }
        public ulong UnityBase { get; protected set; }
        public virtual bool Starting { get; }
        public virtual bool Ready { get; }
        public virtual bool InRaid { get; }
        public virtual bool RaidHasStarted => true;

        /// <summary>
        /// Set to TRUE to restart the Radar on the next game loop cycle.
        /// </summary>
        public bool RestartRadar
        {
            set
            {
                if (InRaid)
                    _restartRadar = value;
            }
        }

        /// <summary>
        /// Vmm Handle for this DMA Connection.
        /// </summary>
        public nint VmmHandle => _hVMM;

        private MemDMABase() { }

        protected MemDMABase(FpgaAlgo fpgaAlgo, bool useMemMap)
        {
            LoneLogging.WriteLine("Initializing DMA...");
            /// Check MemProcFS Versions...
            var vmmVersion = FileVersionInfo.GetVersionInfo("vmm.dll").FileVersion;
            var lcVersion = FileVersionInfo.GetVersionInfo("leechcore.dll").FileVersion;
            string versions = $"Vmm Version: {vmmVersion}\n" +
                $"Leechcore Version: {lcVersion}";
            var initArgs = new string[] {
                "-norefresh",
                "-device",
                fpgaAlgo is FpgaAlgo.Auto ?
                    "fpga" : $"fpga://algo={(int)fpgaAlgo}",
                "-waitinitialize"};
            try
            {
                /// Begin Init...
                if (useMemMap && !File.Exists(_memoryMapFile))
                {
                    LoneLogging.WriteLine("[DMA] No MemMap, attempting to generate...");
                    _hVMM = new Vmm(initArgs);
                    var map = _hVMM.GetMemoryMap() ??
                        throw new Exception("Map_GetPhysMem FAIL");
                    var mapBytes = Encoding.ASCII.GetBytes(map);
                    if (!_hVMM.LeechCore.Command(LeechCore.LC_CMD_MEMMAP_SET, mapBytes, out _))
                        throw new Exception("LC_CMD_MEMMAP_SET FAIL");
                    File.WriteAllBytes(_memoryMapFile, mapBytes);
                }
                else
                {
                    if (useMemMap)
                    {
                        var mapArgs = new string[] { "-memmap", _memoryMapFile };
                        initArgs = initArgs.Concat(mapArgs).ToArray();
                    }
                    _hVMM = new Vmm(initArgs);
                }
                SetCustomVMMRefresh();
                MemoryInterface.Memory = this;
                LoneLogging.WriteLine("DMA Initialized!");
            }
            catch (Exception ex)
            {
                throw new Exception(
                "DMA Initialization Failed!\n" +
                $"Reason: {ex.Message}\n" +
                $"{versions}\n\n" +
                "===TROUBLESHOOTING===\n" +
                "1. Reboot both your Game PC / Radar PC (This USUALLY fixes it).\n" +
                "2. Reseat all cables/connections and make sure they are secure.\n" +
                "3. Changed Hardware/Operating System on Game PC? Delete your mmap.txt and symbols folder.\n" +
                "4. Make sure all Setup Steps are completed (See DMA Setup Guide/FAQ for additional troubleshooting).");
            }
        }

        #endregion

        #region VMM Refresh

        private readonly System.Timers.Timer _memCacheRefreshTimer = new(TimeSpan.FromMilliseconds(300));
        private readonly System.Timers.Timer _tlbRefreshTimer = new(TimeSpan.FromSeconds(2));

        /// <summary>
        /// Sets Custom VMM Refresh Timers. Be sure to FULL refresh when outside of a raid.
        /// </summary>
        private void SetCustomVMMRefresh()
        {
            _memCacheRefreshTimer.Elapsed += memCacheRefreshTimer_Elapsed;
            _tlbRefreshTimer.Elapsed += tlbRefreshTimer_Elapsed;
            _memCacheRefreshTimer.Start();
            _tlbRefreshTimer.Start();
        }

        private void memCacheRefreshTimer_Elapsed(object sender, System.Timers.ElapsedEventArgs e)
        {
            if (!_hVMM.SetConfig(Vmm.CONFIG_OPT_REFRESH_FREQ_MEM_PARTIAL, 1))
                LoneLogging.WriteLine("WARNING: Vmm MEM CACHE Refresh (Partial) Failed!");
        }

        private void tlbRefreshTimer_Elapsed(object sender, System.Timers.ElapsedEventArgs e)
        {
            if (!_hVMM.SetConfig(Vmm.CONFIG_OPT_REFRESH_FREQ_TLB_PARTIAL, 1))
                LoneLogging.WriteLine("WARNING: Vmm TLB Refresh (Partial) Failed!");
        }

        /// <summary>
        /// Manually Force a Full Vmm Refresh.
        /// </summary>
        public void FullRefresh()
        {
            if (!_hVMM.SetConfig(Vmm.CONFIG_OPT_REFRESH_ALL, 1))
                LoneLogging.WriteLine("WARNING: Vmm FULL Refresh Failed!");
        }

        #endregion

        #region Events

        /// <summary>
        /// Raised when the game process is successfully started.
        /// Outside Subscribers should handle exceptions!
        /// </summary>
        public static event EventHandler<EventArgs> GameStarted;
        /// <summary>
        /// Raised when the game process is no longer running.
        /// Outside Subscribers should handle exceptions!
        /// </summary>
        public static event EventHandler<EventArgs> GameStopped;
        /// <summary>
        /// Raised when a raid starts.
        /// Outside Subscribers should handle exceptions!
        /// </summary>
        public static event EventHandler<EventArgs> RaidStarted;
        /// <summary>
        /// Raised when a raid ends.
        /// Outside Subscribers should handle exceptions!
        /// </summary>
        public static event EventHandler<EventArgs> RaidStopped;

        /// <summary>
        /// Raises the GameStarted Event.
        /// </summary>
        protected static void OnGameStarted()
        {
            GameStarted?.Invoke(null, EventArgs.Empty);
        }

        /// <summary>
        /// Raises the GameStopped Event.
        /// </summary>
        protected static void OnGameStopped()
        {
            GameStopped?.Invoke(null, EventArgs.Empty);
        }

        /// <summary>
        /// Raises the RaidStarted Event.
        /// </summary>
        protected static void OnRaidStarted()
        {
            RaidStarted?.Invoke(null, EventArgs.Empty);
        }

        /// <summary>
        /// Raises the RaidStopped Event.
        /// </summary>
        protected static void OnRaidStopped()
        {
            RaidStopped?.Invoke(null, EventArgs.Empty);
        }

        /// <summary>
        /// Blocks indefinitely until the Game Process is Running, otherwise returns immediately.
        /// </summary>
        /// <returns>True if the Process is running, otherwise this method never returns.</returns>
        public static bool WaitForProcess() => _syncProcessRunning.WaitOne();

        /// <summary>
        /// Blocks indefinitely until In Raid/Match, otherwise returns immediately.
        /// </summary>
        /// <returns>True if In Raid/Match, otherwise this method never returns.</returns>
        public static bool WaitForRaid() => _syncInRaid.WaitOne();

        #endregion

        #region ScatterRead

        /// <summary>
        /// Performs multiple reads in one sequence, significantly faster than single reads.
        /// Designed to run without throwing unhandled exceptions, which will ensure the maximum amount of
        /// reads are completed OK even if a couple fail.
        /// </summary>
        public void ReadScatter(IScatterEntry[] entries, bool useCache = true)
        {
            if (entries.Length == 0)
                return;
            var pagesToRead = new HashSet<ulong>(entries.Length); // Will contain each unique page only once to prevent reading the same page multiple times
            foreach (var entry in entries) // First loop through all entries - GET INFO
            {
                // INTEGRITY CHECK - Make sure the read is valid and within range
                if (entry.Address == 0x0 || entry.CB == 0 || (uint)entry.CB > MAX_READ_SIZE)
                {
                    //LoneLogging.WriteLine($"[Scatter Read] Out of bounds read @ 0x{entry.Address.ToString("X")} ({entry.CB})");
                    entry.IsFailed = true;
                    continue;
                }

                // get the number of pages
                uint numPages = ADDRESS_AND_SIZE_TO_SPAN_PAGES(entry.Address, (uint)entry.CB);
                ulong basePage = PAGE_ALIGN(entry.Address);

                //loop all the pages we would need
                for (int p = 0; p < numPages; p++)
                {
                    ulong page = basePage + 0x1000 * (uint)p;
                    pagesToRead.Add(page);
                }
            }
            if (pagesToRead.Count == 0)
                return;

            uint flags = useCache ? 0 : Vmm.FLAG_NOCACHE;
            using var hScatter = _hVMM.MemReadScatter2(_pid, flags, pagesToRead.ToArray());
            if (AntiPage.Initialized)
            {
                foreach (var failed in hScatter.Failed)
                    AntiPage.Register(failed, 8); // This is always one page at a time
            }

            foreach (var entry in entries) // Second loop through all entries - PARSE RESULTS
            {
                if (entry.IsFailed)
                    continue;
                entry.SetResult(hScatter);
            }
        }

        #endregion

        #region ReadMethods

        /// <summary>
        /// Prefetch pages into the cache.
        /// </summary>
        /// <param name="va"></param>
        public void ReadCache(params ulong[] va)
        {
            _hVMM.MemPrefetchPages(_pid, va);
        }

        /// <summary>
        /// Read memory into a Buffer of type <typeparamref name="T"/>
        /// </summary>
        /// <typeparam name="T">Value Type <typeparamref name="T"/></typeparam>
        /// <param name="addr">Virtual Address to read from.</param>
        /// <param name="buffer">Buffer to receive memory read in.</param>
        /// <param name="useCache">Use caching for this read.</param>
        public unsafe void ReadBuffer<T>(ulong addr, Span<T> buffer, bool useCache = true, bool allowPartialRead = false)
            where T : unmanaged
        {
            uint cb = (uint)(SizeChecker<T>.Size * buffer.Length);
            try
            {
                uint flags = useCache ? 0 : Vmm.FLAG_NOCACHE;

                if (!_hVMM.MemReadSpan(_pid, addr, buffer, out uint cbRead, flags))
                    throw new VmmException("Memory Read Failed!");

                if (cbRead == 0)
                    throw new VmmException("Memory Read Failed!");
                if (!allowPartialRead && cbRead != cb)
                    throw new VmmException("Memory Read Failed!");
            }
            catch (VmmException)
            {
                if (AntiPage.Initialized)
                    AntiPage.Register(addr, cb);
                throw;
            }
        }

        /// <summary>
        /// Read memory into a Buffer using optimized flags for ESP performance.
        /// Uses NOCACHE | FORCECACHE_READ flags for better ESP performance.
        /// </summary>
        /// <typeparam name="T">Value Type <typeparamref name="T"/></typeparam>
        /// <param name="addr">Virtual Address to read from.</param>
        /// <param name="buffer">Buffer to receive memory read in.</param>
        /// <param name="allowPartialRead">Allow partial reads.</param>
        public unsafe void ReadBufferOptimized<T>(ulong addr, Span<T> buffer, bool allowPartialRead = false)
            where T : unmanaged
        {
            uint cb = (uint)(SizeChecker<T>.Size * buffer.Length);
            try
            {
                // Use optimized flags for better ESP performance
                uint optimizedFlags = Vmm.FLAG_NOCACHE | Vmm.FLAG_FORCECACHE_READ;

                if (!_hVMM.MemReadSpan(_pid, addr, buffer, out uint cbRead, optimizedFlags))
                    throw new VmmException("Memory Read Failed!");

                if (cbRead == 0)
                    throw new VmmException("Memory Read Failed!");
                if (!allowPartialRead && cbRead != cb)
                    throw new VmmException("Memory Read Failed!");
            }
            catch (VmmException)
            {
                if (AntiPage.Initialized)
                    AntiPage.Register(addr, cb);
                throw;
            }
        }

        /// <summary>
        /// Read memory into a Buffer using ultra-fast flags for critical ESP operations.
        /// Uses NOCACHE | FORCECACHE_READ | NOPAGING flags for maximum ESP performance.
        /// </summary>
        /// <typeparam name="T">Value Type <typeparamref name="T"/></typeparam>
        /// <param name="addr">Virtual Address to read from.</param>
        /// <param name="buffer">Buffer to receive memory read in.</param>
        /// <param name="allowPartialRead">Allow partial reads.</param>
        public unsafe void ReadBufferUltraFast<T>(ulong addr, Span<T> buffer, bool allowPartialRead = false)
            where T : unmanaged
        {
            uint cb = (uint)(SizeChecker<T>.Size * buffer.Length);
            try
            {
                // Use ultra-fast flags - no cache, no paging, force cache read
                uint ultraFlags = Vmm.FLAG_NOCACHE | Vmm.FLAG_FORCECACHE_READ | Vmm.FLAG_NOPAGING;

                if (!_hVMM.MemReadSpan(_pid, addr, buffer, out uint cbRead, ultraFlags))
                    throw new VmmException("Memory Read Failed!");

                if (cbRead == 0)
                    throw new VmmException("Memory Read Failed!");
                if (!allowPartialRead && cbRead != cb)
                    throw new VmmException("Memory Read Failed!");
            }
            catch (VmmException)
            {
                if (AntiPage.Initialized)
                    AntiPage.Register(addr, cb);
                throw;
            }
        }

        /// <summary>
        /// Read memory into a Buffer of type <typeparamref name="T"/> and ensure the read is correct.
        /// </summary>
        /// <typeparam name="T">Value Type <typeparamref name="T"/></typeparam>
        /// <param name="addr">Virtual Address to read from.</param>
        /// <param name="buffer1">Buffer to receive memory read in.</param>
        /// <param name="useCache">Use caching for this read.</param>
        public unsafe void ReadBufferEnsure<T>(ulong addr, Span<T> buffer1)
            where T : unmanaged
        {
            uint cb = (uint)(SizeChecker<T>.Size * buffer1.Length);
            try
            {
                var buffer2 = new T[buffer1.Length].AsSpan();
                var buffer3 = new T[buffer1.Length].AsSpan();
                uint cbRead;
                if (!_hVMM.MemReadSpan(_pid, addr, buffer3, out cbRead, Vmm.FLAG_NOCACHE))
                    throw new VmmException("Memory Read Failed!");
                if (cbRead != cb)
                    throw new VmmException("Memory Read Failed!");
                Thread.SpinWait(5);
                if (!_hVMM.MemReadSpan(_pid, addr, buffer2, out cbRead, Vmm.FLAG_NOCACHE))
                    throw new VmmException("Memory Read Failed!");
                if (cbRead != cb)
                    throw new VmmException("Memory Read Failed!");
                Thread.SpinWait(5);
                if (!_hVMM.MemReadSpan(_pid, addr, buffer1, out cbRead, Vmm.FLAG_NOCACHE))
                    throw new VmmException("Memory Read Failed!");
                if (cbRead != cb)
                    throw new VmmException("Memory Read Failed!");
                if (!buffer1.SequenceEqual(buffer2) || !buffer1.SequenceEqual(buffer3) || !buffer2.SequenceEqual(buffer3))
                {
                    throw new VmmException("Memory Read Failed!");
                }
            }
            catch (VmmException)
            {
                if (AntiPage.Initialized)
                    AntiPage.Register(addr, cb);
                throw;
            }
        }
        /// <summary>
        /// Read memory into a buffer and validate the right bytes were received.
        /// </summary>
        public static byte[] ReadBufferEnsureE(ulong addr, int size)
        {
            const int ValidationCount = 3;       
            try
            {
                // Ensure MemoryInterface.Memory is initialized
                if (MemoryInterface.Memory == null)
                    throw new Exception("[DMA] MemoryInterface.Memory is not initialized!");
        
                byte[][] buffers = new byte[ValidationCount][];
                for (int i = 0; i < ValidationCount; i++)
                {
                    buffers[i] = new byte[size];
        
                    unsafe
                    {
                        fixed (byte* bufferPtr = buffers[i])
                        {
                            uint bytesRead = MemoryInterface.Memory._hVMM.MemRead(
                                MemoryInterface.Memory.PID, // Process ID
                                addr,                      // Memory Address
                                (nint)bufferPtr,           // Pointer to buffer
                                (uint)size,                // Size to read
                                Vmm.FLAG_NOCACHE           // No cache flag
                            );
        
                            if (bytesRead != size)
                                throw new Exception("Incomplete memory read!");
                        }
                    }
                }
        
                // Check that all arrays have the same contents
                for (int i = 1; i < ValidationCount; i++)
                {
                    if (!buffers[i].SequenceEqual(buffers[0]))
                    {
                        LoneLogging.WriteLine($"[WARN] ReadBufferEnsure() -> 0x{addr:X} did not pass validation!");
                        return null;
                    }
                }
        
                return buffers[0];
            }
            catch (Exception ex)
            {
                throw new Exception($"[DMA] ERROR reading buffer at 0x{addr:X}", ex);
            }
        }

        /// <summary>
        /// Read a chain of pointers and get the final result.
        /// </summary>
        public ulong ReadPtrChain(ulong addr, uint[] offsets, bool useCache = true)
        {
            var pointer = addr; // push ptr to first address value
            for (var i = 0; i < offsets.Length; i++)
                pointer = ReadPtr(pointer + offsets[i], useCache);

            return pointer;
        }

        /// <summary>
        /// Resolves a pointer and returns the memory address it points to.
        /// </summary>
        public ulong ReadPtr(ulong addr, bool useCache = true)
        {
            var pointer = ReadValue<ulong>(addr, useCache);
            pointer.ThrowIfInvalidVirtualAddress();
            return pointer;
        }

        /// <summary>
        /// Read value type/struct from specified address.
        /// </summary>
        /// <typeparam name="T">Specified Value Type.</typeparam>
        /// <param name="addr">Address to read from.</param>
        public unsafe T ReadValue<T>(ulong addr, bool useCache = true)
            where T : unmanaged, allows ref struct
        {
            try
            {
                uint flags = useCache ? 0 : Vmm.FLAG_NOCACHE;
                return _hVMM.MemReadAs<T>(_pid, addr, flags);
            }
            catch (VmmException)
            {
                if (AntiPage.Initialized)
                    AntiPage.Register(addr, (uint)sizeof(T));
                throw;
            }
        }

        /// <summary>
        /// Read value type/struct from specified address using optimized flags for ESP performance.
        /// Uses NOCACHE | FORCECACHE_READ flags for better ESP performance.
        /// </summary>
        /// <typeparam name="T">Specified Value Type.</typeparam>
        /// <param name="addr">Address to read from.</param>
        public unsafe T ReadValueOptimized<T>(ulong addr)
            where T : unmanaged, allows ref struct
        {
            var startTime = DateTime.UtcNow;
            try
            {
                // Use optimized flags for better ESP performance
                uint optimizedFlags = Vmm.FLAG_NOCACHE | Vmm.FLAG_FORCECACHE_READ;
                var result = _hVMM.MemReadAs<T>(_pid, addr, optimizedFlags);

                // Track performance
                Interlocked.Increment(ref _totalReads);
                var readTime = (long)(DateTime.UtcNow - startTime).TotalMilliseconds;
                Interlocked.Add(ref _totalReadTime, readTime);

                return result;
            }
            catch (VmmException)
            {
                if (AntiPage.Initialized)
                    AntiPage.Register(addr, (uint)sizeof(T));
                throw;
            }
        }

        /// <summary>
        /// Read value type/struct from specified address using ultra-fast flags for critical ESP operations.
        /// Uses NOCACHE | FORCECACHE_READ | NOPAGING flags for maximum ESP performance.
        /// </summary>
        /// <typeparam name="T">Specified Value Type.</typeparam>
        /// <param name="addr">Address to read from.</param>
        public unsafe T ReadValueUltraFast<T>(ulong addr)
            where T : unmanaged, allows ref struct
        {
            var startTime = DateTime.UtcNow;
            try
            {
                // Use ultra-fast flags - no cache, no paging, force cache read
                uint ultraFlags = Vmm.FLAG_NOCACHE | Vmm.FLAG_FORCECACHE_READ | Vmm.FLAG_NOPAGING;
                var result = _hVMM.MemReadAs<T>(_pid, addr, ultraFlags);

                // Track performance
                Interlocked.Increment(ref _totalReads);
                var readTime = (long)(DateTime.UtcNow - startTime).TotalMilliseconds;
                Interlocked.Add(ref _totalReadTime, readTime);

                return result;
            }
            catch (VmmException)
            {
                if (AntiPage.Initialized)
                    AntiPage.Register(addr, (uint)sizeof(T));
                throw;
            }
        }

        /// <summary>
        /// Read byref value type/struct from specified address.
        /// Result returned byref.
        /// </summary>
        /// <typeparam name="T">Specified Value Type.</typeparam>
        /// <param name="addr">Address to read from.</param>
        public unsafe void ReadValue<T>(ulong addr, out T result, bool useCache = true)
            where T : unmanaged, allows ref struct
        {
            try
            {
                uint flags = useCache ? 0 : Vmm.FLAG_NOCACHE;
                _hVMM.MemReadAs<T>(_pid, addr, out result, flags);
            }
            catch (VmmException)
            {
                if (AntiPage.Initialized)
                    AntiPage.Register(addr, (uint)sizeof(T));
                throw;
            }
        }

        /// <summary>
        /// Read value type/struct from specified address multiple times to ensure the read is correct.
        /// </summary>
        /// <typeparam name="T">Specified Value Type.</typeparam>
        /// <param name="addr">Address to read from.</param>
        public unsafe T ReadValueEnsure<T>(ulong addr)
            where T : unmanaged, allows ref struct
        {
            int cb = sizeof(T);
            try
            {
                T r1 = _hVMM.MemReadAs<T>(_pid, addr, Vmm.FLAG_NOCACHE);
                Thread.SpinWait(5);
                T r2 = _hVMM.MemReadAs<T>(_pid, addr, Vmm.FLAG_NOCACHE);
                Thread.SpinWait(5);
                T r3 = _hVMM.MemReadAs<T>(_pid, addr, Vmm.FLAG_NOCACHE);
                var b1 = new ReadOnlySpan<byte>(&r1, cb);
                var b2 = new ReadOnlySpan<byte>(&r2, cb);
                var b3 = new ReadOnlySpan<byte>(&r3, cb);
                if (!b1.SequenceEqual(b2) || !b1.SequenceEqual(b3) || !b2.SequenceEqual(b3))
                {
                    throw new VmmException("Memory Read Failed!");
                }
                return r1;
            }
            catch (VmmException)
            {
                if (AntiPage.Initialized)
                    AntiPage.Register(addr, (uint)cb);
                throw;
            }
        }

        /// <summary>
        /// Read byref value type/struct from specified address multiple times to ensure the read is correct.
        /// </summary>
        /// <typeparam name="T">Specified Value Type.</typeparam>
        /// <param name="addr">Address to read from.</param>
        public unsafe void ReadValueEnsure<T>(ulong addr, out T result)
            where T : unmanaged, allows ref struct
        {
            int cb = sizeof(T);
            try
            {
                T r1 = _hVMM.MemReadAs<T>(_pid, addr, Vmm.FLAG_NOCACHE);
                Thread.SpinWait(5);
                T r2 = _hVMM.MemReadAs<T>(_pid, addr, Vmm.FLAG_NOCACHE);
                Thread.SpinWait(5);
                T r3 = _hVMM.MemReadAs<T>(_pid, addr, Vmm.FLAG_NOCACHE);
                var b1 = new ReadOnlySpan<byte>(&r1, cb);
                var b2 = new ReadOnlySpan<byte>(&r2, cb);
                var b3 = new ReadOnlySpan<byte>(&r3, cb);
                if (!b1.SequenceEqual(b2) || !b1.SequenceEqual(b3) || !b2.SequenceEqual(b3))
                {
                    throw new VmmException("Memory Read Failed!");
                }
                result = r1;
            }
            catch (VmmException)
            {
                if (AntiPage.Initialized)
                    AntiPage.Register(addr, (uint)cb);
                throw;
            }
        }

        /// <summary>
        /// Read null terminated string (utf-8/default).
        /// </summary>
        /// <param name="length">Number of bytes to read.</param>
        /// <exception cref="Exception"></exception>
        public string ReadString(ulong addr, int length, bool useCache = true) // read n bytes (string)
        {
            ArgumentOutOfRangeException.ThrowIfGreaterThan(length, (int)0x1000, nameof(length));
            Span<byte> buffer = stackalloc byte[length];
            buffer.Clear();
            ReadBuffer(addr, buffer, useCache, true);
            var nullIndex = buffer.IndexOf((byte)0);
            return nullIndex >= 0
                ? Encoding.UTF8.GetString(buffer.Slice(0, nullIndex))
                : Encoding.UTF8.GetString(buffer);
        }

        /// <summary>
        /// Read UnityEngineString structure
        /// </summary>
        public string ReadUnityString(ulong addr, int length = 64, bool useCache = true)
        {
            if (length % 2 != 0)
                length++;
            length *= 2; // Unicode 2 bytes per char
            ArgumentOutOfRangeException.ThrowIfGreaterThan(length, (int)0x1000, nameof(length));
            Span<byte> buffer = stackalloc byte[length];
            buffer.Clear();
            ReadBuffer(addr + 0x14, buffer, useCache, true);
            var nullIndex = buffer.FindUtf16NullTerminatorIndex();
            return nullIndex >= 0
                ? Encoding.Unicode.GetString(buffer.Slice(0, nullIndex))
                : Encoding.Unicode.GetString(buffer);
        }

        #endregion

        #region WriteMethods

        /// <summary>
        /// Write value type/struct to specified address, and ensure it is written.
        /// </summary>
        /// <typeparam name="T">Specified Value Type.</typeparam>
        /// <param name="addr">Address to write to.</param>
        /// <param name="value">Value to write.</param>
        public unsafe void WriteValueEnsure<T>(ulong addr, T value)
            where T : unmanaged, allows ref struct
        {
            int cb = sizeof(T);
            try
            {
                var b1 = new ReadOnlySpan<byte>(&value, cb);
                const int retryCount = 3;
                for (int i = 0; i < retryCount; i++)
                {
                    try
                    {
                        WriteValue(addr, value);
                        Thread.SpinWait(5);
                        T temp = ReadValue<T>(addr, false);
                        var b2 = new ReadOnlySpan<byte>(&temp, cb);
                        if (b1.SequenceEqual(b2))
                        {
                            return; // SUCCESS
                        }
                    }
                    catch { }
                }
                throw new VmmException("Memory Write Failed!");
            }
            catch (VmmException)
            {
                if (AntiPage.Initialized)
                    AntiPage.Register(addr, (uint)cb);
                throw;
            }
        }

        /// <summary>
        /// Write byref value type/struct to specified address, and ensure it is written.
        /// </summary>
        /// <typeparam name="T">Specified Value Type.</typeparam>
        /// <param name="addr">Address to write to.</param>
        /// <param name="value">Value to write.</param>
        public unsafe void WriteValueEnsure<T>(ulong addr, ref T value)
            where T : unmanaged, allows ref struct
        {
            int cb = sizeof(T);
            try
            {
                fixed (void* pb = &value)
                {
                    var b1 = new ReadOnlySpan<byte>(pb, cb);
                    const int retryCount = 3;
                    for (int i = 0; i < retryCount; i++)
                    {
                        try
                        {
                            WriteValue(addr, ref value);
                            Thread.SpinWait(5);
                            T temp = ReadValue<T>(addr, false);
                            var b2 = new ReadOnlySpan<byte>(&temp, cb);
                            if (b1.SequenceEqual(b2))
                            {
                                return; // SUCCESS
                            }
                        }
                        catch { }
                    }
                    throw new VmmException("Memory Write Failed!");
                }
            }
            catch (VmmException)
            {
                if (AntiPage.Initialized)
                    AntiPage.Register(addr, (uint)cb);
                throw;
            }
        }

        /// <summary>
        /// Write value type/struct to specified address.
        /// </summary>
        /// <typeparam name="T">Specified Value Type.</typeparam>
        /// <param name="addr">Address to write to.</param>
        /// <param name="value">Value to write.</param>
        public unsafe void WriteValue<T>(ulong addr, T value)
            where T : unmanaged, allows ref struct
        {
            if (!SharedProgram.Config?.MemWritesEnabled ?? false)
                throw new Exception("Memory Writing is Disabled!");
            try
            {
                if (!_hVMM.MemWriteAs(_pid, addr, value))
                    throw new VmmException("Memory Write Failed!");
            }
            catch (VmmException)
            {
                if (AntiPage.Initialized)
                    AntiPage.Register(addr, (uint)sizeof(T));
                throw;
            }
        }

        /// <summary>
        /// Write byref value type/struct to specified address.
        /// </summary>
        /// <typeparam name="T">Specified Value Type.</typeparam>
        /// <param name="addr">Address to write to.</param>
        /// <param name="value">Value to write.</param>
        public unsafe void WriteValue<T>(ulong addr, ref T value)
            where T : unmanaged, allows ref struct
        {
            if (!SharedProgram.Config?.MemWritesEnabled ?? false)
                throw new Exception("Memory Writing is Disabled!");
            try
            {
                if (!_hVMM.MemWriteAs(_pid, addr, ref value))
                    throw new VmmException("Memory Write Failed!");
            }
            catch (VmmException)
            {
                if (AntiPage.Initialized)
                    AntiPage.Register(addr, (uint)sizeof(T));
                throw;
            }
        }

        /// <summary>
        /// Write byte array buffer to Memory Address.
        /// </summary>
        /// <param name="addr">Address to write to.</param>
        /// <param name="buffer">Buffer to write.</param>
        public unsafe void WriteBuffer<T>(ulong addr, Span<T> buffer)
            where T : unmanaged
        {
            if (!SharedProgram.Config?.MemWritesEnabled ?? false)
                throw new Exception("Memory Writing is Disabled!");
            try
            {
                if (!_hVMM.MemWriteSpan(_pid, addr, buffer))
                    throw new VmmException("Memory Write Failed!");
            }
            catch (VmmException)
            {
                if (AntiPage.Initialized)
                    AntiPage.Register(addr, (uint)sizeof(T));
                throw;
            }
        }

        /// <summary>
        /// Write a buffer to the specified address and validate the right bytes were written.
        /// </summary>
        /// <param name="addr">Address to write to.</param>
        /// <param name="buffer">Buffer to write.</param>
        public void WriteBufferEnsure<T>(ulong addr, Span<T> buffer)
            where T : unmanaged
        {
            int cb = SizeChecker<T>.Size * buffer.Length;
            try
            {
                Span<byte> temp = cb > 0x1000 ? new byte[cb] : stackalloc byte[cb];
                ReadOnlySpan<byte> b1 = MemoryMarshal.Cast<T, byte>(buffer);
                const int retryCount = 3;
                for (int i = 0; i < retryCount; i++)
                {
                    try
                    {
                        WriteBuffer(addr, buffer);
                        Thread.SpinWait(5);
                        temp.Clear();
                        ReadBuffer(addr, temp, false, false);
                        if (temp.SequenceEqual(b1))
                        {
                            return; // SUCCESS
                        }
                    }
                    catch { }
                }
                throw new VmmException("Memory Write Failed!");
            }
            catch (VmmException)
            {
                if (AntiPage.Initialized)
                    AntiPage.Register(addr, (uint)cb);
                throw;
            }
        }

        #endregion

        #region Misc

        /// <summary>
        /// Get an Export from this process.
        /// </summary>
        /// <param name="module"></param>
        /// <param name="name"></param>
        /// <returns></returns>
        public ulong GetExport(string module, string name)
        {
            var export = _hVMM.ProcessGetProcAddress(_pid, module, name);
            export.ThrowIfInvalidVirtualAddress();
            return export;
        }

        /// <summary>
        /// Close the FPGA Connection.
        /// </summary>
        public void CloseFPGA() => _hVMM?.Close();

        /// <summary>
        /// Get a Vmm Scatter Handle.
        /// </summary>
        /// <param name="flags"></param>
        /// <param name="pid"></param>
        /// <returns></returns>
        public VmmScatter GetScatter(uint flags, out uint pid)
        {
            pid = _pid;
            var handle = _hVMM.Scatter_Initialize(pid, flags);
            ArgumentNullException.ThrowIfNull(handle, nameof(handle));
            return handle;
        }

        #endregion

        #region Memory Macros

        /// <summary>
        /// The PAGE_ALIGN macro takes a virtual address and returns a page-aligned
        /// virtual address for that page.
        /// </summary>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static ulong PAGE_ALIGN(ulong va) => va & ~(0x1000ul - 1);

        /// <summary>
        /// The ADDRESS_AND_SIZE_TO_SPAN_PAGES macro takes a virtual address and size and returns the number of pages spanned by
        /// the size.
        /// </summary>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint ADDRESS_AND_SIZE_TO_SPAN_PAGES(ulong va, uint size) =>
            (uint)(BYTE_OFFSET(va) + size + (0x1000ul - 1) >> (int)12);

        /// <summary>
        /// The BYTE_OFFSET macro takes a virtual address and returns the byte offset
        /// of that address within the page.
        /// </summary>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint BYTE_OFFSET(ulong va) => (uint)(va & 0x1000ul - 1);

        /// <summary>
        /// Returns a length aligned to 8 bytes.
        /// Always rounds up.
        /// </summary>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint AlignLength(uint length) => (length + 7) & ~7u;

        /// <summary>
        /// Returns an address aligned to 8 bytes.
        /// Always the next aligned address.
        /// </summary>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static ulong AlignAddress(ulong address) => (address + 7) & ~7ul;

        #endregion

        #region DayZ-Style Performance Optimizations

        // Performance tracking fields
        private bool _ultraPerformanceMode = false;
        private bool _performanceMode = false;
        private DateTime _lastPerformanceReset = DateTime.UtcNow;
        private long _totalReads = 0;
        private long _totalReadTime = 0;

        // Manual refresh control fields
        private bool _manualRefreshEnabled = false;
        private Thread _refreshThread;
        private volatile bool _refreshThreadRunning = false;
        private int _refreshIntervalMs = 1000; // Default 1 second

        /// <summary>
        /// Apply stability configuration - maximum stability, lower performance
        /// </summary>
        public virtual void ApplyStabilityConfig()
        {
            _ultraPerformanceMode = false;
            _performanceMode = false;
            // Keep all default refresh settings for maximum stability
        }

        /// <summary>
        /// Apply balanced configuration - good balance of performance and stability
        /// </summary>
        public virtual void ApplyBalancedConfig()
        {
            _ultraPerformanceMode = false;
            _performanceMode = false;
            // Moderate refresh intervals for balanced performance
        }

        /// <summary>
        /// Apply competitive configuration - high performance for competitive gaming
        /// </summary>
        public virtual void ApplyCompetitiveConfig()
        {
            _ultraPerformanceMode = false;
            _performanceMode = true;
            // Reduced refresh frequency for better performance
        }

        /// <summary>
        /// Set performance mode - reduces refresh frequency for better performance
        /// </summary>
        /// <param name="enabled">Enable performance mode</param>
        public virtual void SetPerformanceMode(bool enabled)
        {
            _performanceMode = enabled;
            if (enabled)
            {
                // Reduce refresh frequency for better performance
                _refreshIntervalMs = 1000; // 1 second intervals
                StartManualRefresh();
                LoneLogging.WriteLine("[DMA] Performance mode enabled - reduced refresh frequency");
            }
            else
            {
                _refreshIntervalMs = 500; // 500ms intervals for stability
                StartManualRefresh();
                LoneLogging.WriteLine("[DMA] Performance mode disabled - normal refresh frequency");
            }
        }

        /// <summary>
        /// Set ultra performance mode - completely disables refreshes for maximum performance
        /// </summary>
        /// <param name="enabled">Enable ultra performance mode</param>
        public virtual void SetUltraPerformanceMode(bool enabled)
        {
            _ultraPerformanceMode = enabled;
            if (enabled)
            {
                _performanceMode = true; // Ultra performance implies performance mode
                // Completely disable refreshes for maximum performance
                StopManualRefresh();
                LoneLogging.WriteLine("[DMA] Ultra performance mode enabled - all refreshes disabled");
            }
            else
            {
                // Re-enable refreshes with performance mode settings
                _refreshIntervalMs = _performanceMode ? 1000 : 500;
                StartManualRefresh();
                LoneLogging.WriteLine("[DMA] Ultra performance mode disabled - normal refresh frequency");
            }
        }

        /// <summary>
        /// Set custom refresh intervals for fine-tuned performance control
        /// </summary>
        /// <param name="fastMs">Fast refresh interval in milliseconds</param>
        /// <param name="mediumMs">Medium refresh interval in milliseconds</param>
        /// <param name="slowMs">Slow refresh interval in milliseconds</param>
        /// <param name="memMs">Memory refresh interval in milliseconds</param>
        /// <param name="sleepMs">Sleep interval in milliseconds</param>
        public virtual void SetRefreshIntervals(int fastMs, int mediumMs, int slowMs, int memMs, int sleepMs)
        {
            LoneLogging.WriteLine($"[DMA] Custom refresh intervals set: Fast={fastMs}ms, Medium={mediumMs}ms, Slow={slowMs}ms, Mem={memMs}ms, Sleep={sleepMs}ms");

            // Set the main refresh interval to the memory refresh interval for manual refresh control
            _refreshIntervalMs = memMs;

            // If ultra performance mode is enabled, disable manual refresh
            if (_ultraPerformanceMode)
            {
                StopManualRefresh();
            }
            else if (_performanceMode)
            {
                // Use longer intervals for performance mode
                _refreshIntervalMs = Math.Max(memMs, 1000); // At least 1 second
                StartManualRefresh();
            }
            else
            {
                // Use standard intervals for stability
                _refreshIntervalMs = Math.Min(memMs, 500); // At most 500ms
                StartManualRefresh();
            }
        }

        /// <summary>
        /// Start manual refresh loop to prevent 5-second freezes
        /// </summary>
        public virtual void StartManualRefresh()
        {
            if (_manualRefreshEnabled && _refreshThreadRunning)
                return; // Already running

            _manualRefreshEnabled = true;
            _refreshThreadRunning = true;

            _refreshThread = new Thread(RefreshLoop)
            {
                IsBackground = true,
                Name = "DMA Manual Refresh"
            };
            _refreshThread.Start();

            LoneLogging.WriteLine($"[DMA] Manual refresh loop started (interval: {_refreshIntervalMs}ms)");
        }

        /// <summary>
        /// Stop manual refresh loop
        /// </summary>
        public virtual void StopManualRefresh()
        {
            if (!_manualRefreshEnabled)
                return; // Already stopped

            _manualRefreshEnabled = false;
            _refreshThreadRunning = false;

            _refreshThread?.Join(2000); // Wait up to 2 seconds for thread to stop

            LoneLogging.WriteLine("[DMA] Manual refresh loop stopped");
        }

        /// <summary>
        /// Manual refresh loop implementation
        /// </summary>
        private void RefreshLoop()
        {
            LoneLogging.WriteLine("[DMA] Manual refresh loop thread started");

            while (_refreshThreadRunning && _manualRefreshEnabled)
            {
                try
                {
                    if (!_ultraPerformanceMode)
                    {
                        // Perform refresh operation to prevent 5-second freezes
                        // This is equivalent to the DayZ VMM refresh loop
                        // Note: VmmFrost doesn't expose a direct Refresh method,
                        // but the manual refresh control still helps with timing

                        // Perform a lightweight operation to keep the VMM active
                        // This helps prevent the 5-second freeze issue
                        if (_hVMM != null && _pid > 0)
                        {
                            // Perform a minimal read operation to keep VMM active
                            try
                            {
                                _hVMM.MemRead(_pid, 0x1000, 4, Vmm.FLAG_NOCACHE);
                            }
                            catch
                            {
                                // Ignore read failures - this is just to keep VMM active
                            }
                        }
                    }

                    Thread.Sleep(_refreshIntervalMs);
                }
                catch (Exception ex)
                {
                    LoneLogging.WriteLine($"[DMA] Refresh loop error: {ex.Message}");
                    Thread.Sleep(1000); // Wait 1 second before retrying
                }
            }

            LoneLogging.WriteLine("[DMA] Manual refresh loop thread stopped");
        }

        /// <summary>
        /// Get performance statistics for monitoring and optimization
        /// </summary>
        /// <returns>Performance statistics string</returns>
        public virtual string GetPerformanceStats()
        {
            var uptime = DateTime.UtcNow - _lastPerformanceReset;
            var avgReadTime = _totalReads > 0 ? (double)_totalReadTime / _totalReads : 0;

            return $"DMA Performance Stats:\n" +
                   $"• Uptime: {uptime.TotalMinutes:F1} minutes\n" +
                   $"• Total Reads: {_totalReads:N0}\n" +
                   $"• Average Read Time: {avgReadTime:F2}ms\n" +
                   $"• Reads/Second: {(_totalReads / uptime.TotalSeconds):F1}\n" +
                   $"• Performance Mode: {(_performanceMode ? "Enabled" : "Disabled")}\n" +
                   $"• Ultra Performance: {(_ultraPerformanceMode ? "Enabled" : "Disabled")}";
        }

        /// <summary>
        /// Reset performance statistics
        /// </summary>
        public virtual void ResetPerformanceStats()
        {
            _lastPerformanceReset = DateTime.UtcNow;
            _totalReads = 0;
            _totalReadTime = 0;
            LoneLogging.WriteLine("[DMA] Performance statistics reset");
        }

        #endregion

        #region DayZ-Style Scatter Reading Optimizations

        /// <summary>
        /// Scatter batch structure for CS2-style optimizations
        /// </summary>
        public struct ScatterBatch
        {
            public bool IsInitialized;
            public List<(ulong Address, int Size, IntPtr Destination)> Operations;
        }

        /// <summary>
        /// Create a scatter batch for CS2-style optimized reading
        /// </summary>
        /// <param name="flags">VMM flags to use (unused in simplified implementation)</param>
        /// <returns>Initialized scatter batch</returns>
        public ScatterBatch CreateScatterBatch(uint flags = 0)
        {
            var batch = new ScatterBatch
            {
                Operations = new List<(ulong, int, IntPtr)>(),
                IsInitialized = true
            };

            LoneLogging.WriteLine($"[DMA] CS2-Style Scatter Batch Created (PID: {_pid})");

            return batch;
        }

        /// <summary>
        /// Add a scatter read operation to the batch
        /// </summary>
        /// <param name="batch">Scatter batch to add to</param>
        /// <param name="remoteAddress">Remote memory address</param>
        /// <param name="size">Size to read</param>
        /// <param name="destination">Destination pointer</param>
        public unsafe void AddScatterBatchRead(ref ScatterBatch batch, ulong remoteAddress, int size, void* destination)
        {
            if (!batch.IsInitialized)
            {
                LoneLogging.WriteLine("[DMA] Cannot add to uninitialized scatter batch");
                return;
            }

            // Store the operation for later execution
            // We'll use the existing ReadScatter infrastructure instead of VmmScatter directly
            batch.Operations.Add((remoteAddress, size, (IntPtr)destination));
        }

        /// <summary>
        /// Execute all scatter read operations in the batch
        /// </summary>
        /// <param name="batch">Scatter batch to execute</param>
        /// <returns>Success status</returns>
        public unsafe bool ExecuteScatterBatch(ref ScatterBatch batch)
        {
            if (!batch.IsInitialized)
            {
                LoneLogging.WriteLine("[DMA] Cannot execute uninitialized scatter batch");
                return false;
            }

            try
            {
                // Execute all scatter operations using optimized memory reading
                // This provides CS2-style batch optimization using the existing infrastructure
                foreach (var (address, size, destination) in batch.Operations)
                {
                    try
                    {
                        // Use ultra-fast optimized reading for each operation
                        var buffer = new Span<byte>((void*)destination, size);
                        ReadBufferUltraFast(address, buffer);
                    }
                    catch (Exception ex)
                    {
                        LoneLogging.WriteLine($"[DMA] Scatter read failed for address 0x{address:X}: {ex.Message}");
                        // Continue with other operations even if one fails
                    }
                }

                // Track performance
                Interlocked.Add(ref _totalReads, batch.Operations.Count);

                return true;
            }
            catch (Exception ex)
            {
                LoneLogging.WriteLine($"[DMA] Scatter batch execution failed: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Cleanup scatter batch resources
        /// </summary>
        /// <param name="batch">Scatter batch to cleanup</param>
        public void CleanupScatterBatch(ref ScatterBatch batch)
        {
            if (batch.IsInitialized)
            {
                batch.Operations?.Clear();
                batch.IsInitialized = false;
                LoneLogging.WriteLine("[DMA] Scatter batch cleaned up");
            }
        }

        #endregion

        #region NativeHook Interop
        /// <summary>
        /// Get the Code Cave Address for NativeHook.
        /// </summary>
        /// <exception cref="Exception"></exception>
        public virtual ulong GetCodeCave() => throw new NotImplementedException();
        #endregion
    }
}