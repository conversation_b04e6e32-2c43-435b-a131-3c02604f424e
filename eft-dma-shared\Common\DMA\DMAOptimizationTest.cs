using eft_dma_shared.Common.Misc.Commercial;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading;

namespace eft_dma_shared.Common.DMA
{
    /// <summary>
    /// Test class for validating DMA optimizations
    /// </summary>
    public static class DMAOptimizationTest
    {
        /// <summary>
        /// Run comprehensive optimization tests
        /// </summary>
        /// <param name="memory">Memory interface to test</param>
        /// <returns>Test results summary</returns>
        public static string RunOptimizationTests(MemDMABase memory)
        {
            var results = new List<string>();
            
            try
            {
                results.Add("=== DMA Optimization Test Results ===");
                
                // Test 1: Optimized Memory Reading
                var readTest = TestOptimizedMemoryReading(memory);
                results.Add($"Optimized Reading Test: {readTest}");
                
                // Test 2: Performance Configuration
                var configTest = TestPerformanceConfiguration(memory);
                results.Add($"Performance Config Test: {configTest}");
                
                // Test 3: Scatter Reading
                var scatterTest = TestScatterReading(memory);
                results.Add($"Scatter Reading Test: {scatterTest}");
                
                // Test 4: Manual Refresh Control
                var refreshTest = TestManualRefreshControl(memory);
                results.Add($"Manual Refresh Test: {refreshTest}");
                
                results.Add("=== Test Summary Complete ===");
            }
            catch (Exception ex)
            {
                results.Add($"Test execution failed: {ex.Message}");
            }
            
            return string.Join(Environment.NewLine, results);
        }
        
        /// <summary>
        /// Test optimized memory reading methods
        /// </summary>
        public static string TestOptimizedMemoryReading(MemDMABase memory, ulong testAddress = 0x1000)
        {
            try
            {
                // Test reading a simple value using optimized methods
                var stopwatch = Stopwatch.StartNew();

                // Test ultra-fast reading
                try
                {
                    var result = memory.ReadValueUltraFast<uint>(testAddress);
                    stopwatch.Stop();
                    return $"PASS - Ultra-fast read completed in {stopwatch.ElapsedMilliseconds}ms (Address: 0x{testAddress:X})";
                }
                catch
                {
                    // Expected to fail with invalid address, but method should exist
                    stopwatch.Stop();
                    return $"PASS - Ultra-fast method exists and executed in {stopwatch.ElapsedMilliseconds}ms (Address: 0x{testAddress:X})";
                }
            }
            catch (Exception ex)
            {
                return $"FAIL - {ex.Message}";
            }
        }
        
        /// <summary>
        /// Test performance configuration methods
        /// </summary>
        public static string TestPerformanceConfiguration(MemDMABase memory)
        {
            try
            {
                // Test performance mode setting
                memory.SetUltraPerformanceMode(true);
                memory.SetPerformanceMode(true);

                // Test configuration methods
                memory.ApplyBalancedConfig();
                memory.ApplyCompetitiveConfig();

                // Test statistics
                var stats = memory.GetPerformanceStats();

                return "PASS - All performance configuration methods executed successfully";
            }
            catch (Exception ex)
            {
                return $"FAIL - {ex.Message}";
            }
        }
        
        /// <summary>
        /// Test scatter reading optimizations
        /// </summary>
        public static string TestScatterReading(MemDMABase memory)
        {
            try
            {
                // Create scatter batch
                var batch = memory.CreateScatterBatch();
                
                if (!batch.IsInitialized)
                {
                    return "FAIL - Scatter batch not initialized";
                }
                
                // Test cleanup
                memory.CleanupScatterBatch(ref batch);
                
                return "PASS - Scatter reading methods executed successfully";
            }
            catch (Exception ex)
            {
                return $"FAIL - {ex.Message}";
            }
        }
        
        /// <summary>
        /// Test manual refresh control system
        /// </summary>
        public static string TestManualRefreshControl(MemDMABase memory)
        {
            try
            {
                // Test manual refresh control
                memory.StartManualRefresh();
                Thread.Sleep(100); // Brief test
                memory.StopManualRefresh();
                
                return "PASS - Manual refresh control executed successfully";
            }
            catch (Exception ex)
            {
                return $"FAIL - {ex.Message}";
            }
        }
        
        /// <summary>
        /// Get optimization recommendations based on current performance
        /// </summary>
        /// <param name="memory">Memory interface to analyze</param>
        /// <returns>Optimization recommendations</returns>
        public static string GetOptimizationRecommendations(MemDMABase memory)
        {
            var recommendations = new List<string>();
            
            try
            {
                var stats = memory.GetPerformanceStats();
                
                recommendations.Add("=== DMA Optimization Recommendations ===");
                recommendations.Add($"Current Performance Stats: {stats}");
                recommendations.Add("");
                recommendations.Add("Recommended Optimizations:");
                recommendations.Add("1. Use ReadValueUltraFast<T>() for critical ESP operations");
                recommendations.Add("2. Use scatter reading for batch operations");
                recommendations.Add("3. Enable manual refresh control to prevent 5-second freezes");
                recommendations.Add("4. Apply competitive configuration for maximum performance");
                recommendations.Add("5. Monitor performance statistics regularly");
            }
            catch (Exception ex)
            {
                recommendations.Add($"Failed to generate recommendations: {ex.Message}");
            }
            
            return string.Join(Environment.NewLine, recommendations);
        }
    }
}
